#include <iostream>
#include <thread>
#include <chrono>
#include <atomic>
#include <vector>
#include <future>
#include <functional>
#include <cassert>
#include <queue>
#include <mutex>
#include <condition_variable>
#include <random>

using namespace std::chrono_literals;

// Test framework
#define TEST(name) void test_##name()
#define EXPECT_EQ(a, b) assert((a) == (b))
#define EXPECT_TRUE(a) assert(a)
#define EXPECT_FALSE(a) assert(!(a))
#define EXPECT_GT(a, b) assert((a) > (b))
#define EXPECT_LT(a, b) assert((a) < (b))

// Advanced async executor with priority support
class PriorityAsyncExecutor {
public:
    enum class Priority { Low = 0, Normal = 1, High = 2, Critical = 3 };
    
private:
    struct Task {
        std::function<void()> func;
        Priority priority;
        
        bool operator<(const Task& other) const {
            return priority < other.priority; // Higher priority first
        }
    };
    
    std::vector<std::thread> threads_;
    std::priority_queue<Task> task_queue_;
    std::mutex queue_mutex_;
    std::condition_variable cv_;
    std::atomic<bool> running_{true};
    std::atomic<size_t> pending_tasks_{0};
    
public:
    PriorityAsyncExecutor(size_t num_threads = 4) {
        for (size_t i = 0; i < num_threads; ++i) {
            threads_.emplace_back([this]() {
                while (running_) {
                    std::unique_lock<std::mutex> lock(queue_mutex_);
                    cv_.wait(lock, [this]() { return !task_queue_.empty() || !running_; });
                    
                    if (!running_) break;
                    
                    if (!task_queue_.empty()) {
                        auto task = std::move(const_cast<Task&>(task_queue_.top()));
                        task_queue_.pop();
                        lock.unlock();
                        
                        try {
                            task.func();
                        } catch (...) {
                            // Log error in real implementation
                        }
                        
                        pending_tasks_.fetch_sub(1);
                    }
                }
            });
        }
    }
    
    ~PriorityAsyncExecutor() {
        running_ = false;
        cv_.notify_all();
        for (auto& t : threads_) {
            if (t.joinable()) {
                t.join();
            }
        }
    }
    
    template<typename F>
    auto execute(F&& func, Priority priority = Priority::Normal) -> std::future<std::invoke_result_t<F>> {
        using ReturnType = std::invoke_result_t<F>;
        auto promise = std::make_shared<std::promise<ReturnType>>();
        auto future = promise->get_future();
        
        {
            std::lock_guard<std::mutex> lock(queue_mutex_);
            task_queue_.push({[promise, func = std::forward<F>(func)]() {
                try {
                    if constexpr (std::is_void_v<ReturnType>) {
                        func();
                        promise->set_value();
                    } else {
                        promise->set_value(func());
                    }
                } catch (...) {
                    promise->set_exception(std::current_exception());
                }
            }, priority});
            pending_tasks_.fetch_add(1);
        }
        cv_.notify_one();
        
        return future;
    }
    
    size_t getPendingTaskCount() const {
        return pending_tasks_.load();
    }
    
    void waitForAll() {
        while (pending_tasks_.load() > 0) {
            std::this_thread::sleep_for(1ms);
        }
    }
};

// Message Bus implementation for testing
template<typename MessageType>
class SimpleMessageBus {
private:
    struct Subscriber {
        std::function<void(const MessageType&)> handler;
        std::string id;
    };
    
    std::vector<Subscriber> subscribers_;
    std::mutex subscribers_mutex_;
    std::queue<MessageType> message_queue_;
    std::mutex queue_mutex_;
    std::condition_variable cv_;
    std::thread processing_thread_;
    std::atomic<bool> running_{true};
    
public:
    SimpleMessageBus() : processing_thread_([this]() {
        while (running_) {
            std::unique_lock<std::mutex> lock(queue_mutex_);
            cv_.wait(lock, [this]() { return !message_queue_.empty() || !running_; });
            
            if (!running_) break;
            
            while (!message_queue_.empty()) {
                auto message = std::move(message_queue_.front());
                message_queue_.pop();
                lock.unlock();
                
                // Notify all subscribers
                std::lock_guard<std::mutex> sub_lock(subscribers_mutex_);
                for (const auto& subscriber : subscribers_) {
                    try {
                        subscriber.handler(message);
                    } catch (...) {
                        // Log error in real implementation
                    }
                }
                
                lock.lock();
            }
        }
    }) {}
    
    ~SimpleMessageBus() {
        running_ = false;
        cv_.notify_all();
        if (processing_thread_.joinable()) {
            processing_thread_.join();
        }
    }
    
    void subscribe(const std::string& id, std::function<void(const MessageType&)> handler) {
        std::lock_guard<std::mutex> lock(subscribers_mutex_);
        subscribers_.push_back({std::move(handler), id});
    }
    
    void publish(const MessageType& message) {
        {
            std::lock_guard<std::mutex> lock(queue_mutex_);
            message_queue_.push(message);
        }
        cv_.notify_one();
    }
    
    size_t getSubscriberCount() const {
        std::lock_guard<std::mutex> lock(const_cast<std::mutex&>(subscribers_mutex_));
        return subscribers_.size();
    }
};

// Integration Tests
TEST(PriorityExecutorBasic) {
    PriorityAsyncExecutor executor(2);
    
    std::vector<int> execution_order;
    std::mutex order_mutex;
    
    // Submit tasks with different priorities
    auto low_future = executor.execute([&]() {
        std::lock_guard<std::mutex> lock(order_mutex);
        execution_order.push_back(1);
        return 1;
    }, PriorityAsyncExecutor::Priority::Low);
    
    auto high_future = executor.execute([&]() {
        std::lock_guard<std::mutex> lock(order_mutex);
        execution_order.push_back(2);
        return 2;
    }, PriorityAsyncExecutor::Priority::High);
    
    auto critical_future = executor.execute([&]() {
        std::lock_guard<std::mutex> lock(order_mutex);
        execution_order.push_back(3);
        return 3;
    }, PriorityAsyncExecutor::Priority::Critical);
    
    // Wait for completion
    low_future.wait();
    high_future.wait();
    critical_future.wait();
    
    EXPECT_EQ(execution_order.size(), 3);
    
    std::cout << "✓ PriorityExecutorBasic passed\n";
}

TEST(MessageBusIntegration) {
    SimpleMessageBus<std::string> bus;
    
    std::atomic<int> message_count{0};
    std::vector<std::string> received_messages;
    std::mutex messages_mutex;
    
    // Subscribe multiple handlers
    bus.subscribe("handler1", [&](const std::string& msg) {
        std::lock_guard<std::mutex> lock(messages_mutex);
        received_messages.push_back("h1:" + msg);
        message_count.fetch_add(1);
    });
    
    bus.subscribe("handler2", [&](const std::string& msg) {
        std::lock_guard<std::mutex> lock(messages_mutex);
        received_messages.push_back("h2:" + msg);
        message_count.fetch_add(1);
    });
    
    EXPECT_EQ(bus.getSubscriberCount(), 2);
    
    // Publish messages
    bus.publish("test1");
    bus.publish("test2");
    
    // Wait for processing
    std::this_thread::sleep_for(50ms);
    
    EXPECT_EQ(message_count.load(), 4); // 2 handlers * 2 messages
    
    std::cout << "✓ MessageBusIntegration passed\n";
}

TEST(ExecutorMessageBusIntegration) {
    PriorityAsyncExecutor executor(4);
    SimpleMessageBus<int> bus;
    
    std::atomic<int> processed_count{0};
    std::atomic<int> sum{0};
    
    // Subscribe to messages and process them asynchronously
    bus.subscribe("async_processor", [&](const int& value) {
        executor.execute([&, value]() {
            // Simulate processing
            std::this_thread::sleep_for(1ms);
            sum.fetch_add(value);
            processed_count.fetch_add(1);
        }, PriorityAsyncExecutor::Priority::Normal);
    });
    
    // Publish a series of numbers
    for (int i = 1; i <= 10; ++i) {
        bus.publish(i);
    }
    
    // Wait for all processing to complete
    std::this_thread::sleep_for(100ms);
    executor.waitForAll();
    
    EXPECT_EQ(processed_count.load(), 10);
    EXPECT_EQ(sum.load(), 55); // Sum of 1 to 10
    
    std::cout << "✓ ExecutorMessageBusIntegration passed\n";
}

TEST(ConcurrentStressTest) {
    PriorityAsyncExecutor executor(8);
    SimpleMessageBus<int> bus;
    
    std::atomic<int> total_processed{0};
    std::atomic<long long> checksum{0};
    
    // Multiple subscribers processing different aspects
    bus.subscribe("counter", [&](const int& value) {
        total_processed.fetch_add(1);
    });
    
    bus.subscribe("checksum", [&](const int& value) {
        checksum.fetch_add(value);
    });
    
    bus.subscribe("async_work", [&](const int& value) {
        executor.execute([&, value]() {
            // Simulate CPU-intensive work
            volatile int result = 0;
            for (int i = 0; i < value * 100; ++i) {
                result += i;
            }
        }, PriorityAsyncExecutor::Priority::Low);
    });
    
    // Generate random workload
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(1, 100);
    
    const int num_messages = 1000;
    long long expected_checksum = 0;
    
    for (int i = 0; i < num_messages; ++i) {
        int value = dis(gen);
        expected_checksum += value;
        bus.publish(value);
    }
    
    // Wait for processing
    std::this_thread::sleep_for(200ms);
    executor.waitForAll();
    
    EXPECT_EQ(total_processed.load(), num_messages);
    EXPECT_EQ(checksum.load(), expected_checksum);
    
    std::cout << "✓ ConcurrentStressTest passed (" << num_messages << " messages)\n";
}

TEST(ResourceCleanupTest) {
    // Test that resources are properly cleaned up
    {
        PriorityAsyncExecutor executor(4);
        SimpleMessageBus<std::string> bus;
        
        std::atomic<int> cleanup_counter{0};
        
        // Submit tasks that will be cleaned up
        for (int i = 0; i < 100; ++i) {
            executor.execute([&cleanup_counter]() {
                cleanup_counter.fetch_add(1);
                std::this_thread::sleep_for(1ms);
            });
        }
        
        // Subscribe handlers that will be cleaned up
        for (int i = 0; i < 10; ++i) {
            bus.subscribe("handler" + std::to_string(i), [](const std::string&) {
                // Handler logic
            });
        }
        
        EXPECT_EQ(bus.getSubscriberCount(), 10);
        
        // Let some tasks complete
        std::this_thread::sleep_for(50ms);
        
        // Destructors will be called here
    }
    
    // If we reach here without hanging, cleanup worked
    std::cout << "✓ ResourceCleanupTest passed\n";
}

// Main test runner
int main() {
    std::cout << "Running Integration and Concurrency Tests...\n\n";
    
    try {
        test_PriorityExecutorBasic();
        test_MessageBusIntegration();
        test_ExecutorMessageBusIntegration();
        test_ConcurrentStressTest();
        test_ResourceCleanupTest();
        
        std::cout << "\n✅ All integration tests passed!\n";
        return 0;
    } catch (const std::exception& e) {
        std::cout << "\n❌ Test failed with exception: " << e.what() << "\n";
        return 1;
    } catch (...) {
        std::cout << "\n❌ Test failed with unknown exception\n";
        return 1;
    }
}
