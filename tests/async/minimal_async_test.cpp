#include <iostream>
#include <thread>
#include <chrono>
#include <atomic>
#include <vector>
#include <future>
#include <functional>
#include <cassert>

// Simple test framework
#define TEST(name) void test_##name()
#define EXPECT_EQ(a, b) assert((a) == (b))
#define EXPECT_TRUE(a) assert(a)
#define EXPECT_FALSE(a) assert(!(a))

using namespace std::chrono_literals;

// Simple async executor for testing
class SimpleAsyncExecutor {
private:
    std::vector<std::thread> threads_;
    std::atomic<bool> running_{true};
    
public:
    SimpleAsyncExecutor(size_t num_threads = 4) {
        for (size_t i = 0; i < num_threads; ++i) {
            threads_.emplace_back([this]() {
                while (running_) {
                    std::this_thread::sleep_for(1ms);
                }
            });
        }
    }
    
    ~SimpleAsyncExecutor() {
        running_ = false;
        for (auto& t : threads_) {
            if (t.joinable()) {
                t.join();
            }
        }
    }
    
    template<typename F>
    auto execute(F&& func) -> std::future<std::invoke_result_t<F>> {
        using ReturnType = std::invoke_result_t<F>;
        auto promise = std::make_shared<std::promise<ReturnType>>();
        auto future = promise->get_future();
        
        std::thread([promise, func = std::forward<F>(func)]() {
            try {
                if constexpr (std::is_void_v<ReturnType>) {
                    func();
                    promise->set_value();
                } else {
                    promise->set_value(func());
                }
            } catch (...) {
                promise->set_exception(std::current_exception());
            }
        }).detach();
        
        return future;
    }
};

// Test cases
TEST(BasicAsyncExecution) {
    SimpleAsyncExecutor executor;
    
    std::atomic<bool> executed{false};
    auto future = executor.execute([&executed]() {
        executed = true;
        return 42;
    });
    
    auto result = future.get();
    EXPECT_EQ(result, 42);
    EXPECT_TRUE(executed.load());
    
    std::cout << "✓ BasicAsyncExecution passed\n";
}

TEST(MultipleAsyncTasks) {
    SimpleAsyncExecutor executor;
    
    std::atomic<int> counter{0};
    std::vector<std::future<void>> futures;
    
    for (int i = 0; i < 10; ++i) {
        futures.push_back(executor.execute([&counter]() {
            counter.fetch_add(1);
        }));
    }
    
    for (auto& future : futures) {
        future.wait();
    }
    
    EXPECT_EQ(counter.load(), 10);
    
    std::cout << "✓ MultipleAsyncTasks passed\n";
}

TEST(ExceptionHandling) {
    SimpleAsyncExecutor executor;
    
    auto future = executor.execute([]() -> int {
        throw std::runtime_error("test exception");
        return 42;
    });
    
    try {
        future.get();
        assert(false); // Should not reach here
    } catch (const std::runtime_error& e) {
        EXPECT_EQ(std::string(e.what()), "test exception");
    }
    
    std::cout << "✓ ExceptionHandling passed\n";
}

TEST(ConcurrentAccess) {
    SimpleAsyncExecutor executor;
    
    std::atomic<int> shared_counter{0};
    std::vector<std::future<void>> futures;
    
    // Launch multiple tasks that increment a shared counter
    for (int i = 0; i < 100; ++i) {
        futures.push_back(executor.execute([&shared_counter]() {
            for (int j = 0; j < 10; ++j) {
                shared_counter.fetch_add(1);
            }
        }));
    }
    
    // Wait for all tasks to complete
    for (auto& future : futures) {
        future.wait();
    }
    
    EXPECT_EQ(shared_counter.load(), 1000);
    
    std::cout << "✓ ConcurrentAccess passed\n";
}

TEST(TaskChaining) {
    SimpleAsyncExecutor executor;
    
    auto future1 = executor.execute([]() {
        return 10;
    });
    
    auto result1 = future1.get();
    
    auto future2 = executor.execute([result1]() {
        return result1 * 2;
    });
    
    auto result2 = future2.get();
    EXPECT_EQ(result2, 20);
    
    std::cout << "✓ TaskChaining passed\n";
}

TEST(StressTest) {
    SimpleAsyncExecutor executor;
    
    const int num_tasks = 1000;
    std::atomic<int> completed_tasks{0};
    std::vector<std::future<void>> futures;
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    for (int i = 0; i < num_tasks; ++i) {
        futures.push_back(executor.execute([&completed_tasks]() {
            // Simulate some work
            std::this_thread::sleep_for(std::chrono::microseconds(100));
            completed_tasks.fetch_add(1);
        }));
    }
    
    // Wait for all tasks
    for (auto& future : futures) {
        future.wait();
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    EXPECT_EQ(completed_tasks.load(), num_tasks);
    
    std::cout << "✓ StressTest passed (" << num_tasks << " tasks in " << duration.count() << "ms)\n";
}

// Simple Promise implementation test
class SimplePromise {
private:
    std::promise<int> promise_;
    std::shared_future<int> future_;
    std::atomic<bool> completed_{false};
    
public:
    SimplePromise() : future_(promise_.get_future().share()) {}
    
    void setValue(int value) {
        promise_.set_value(value);
        completed_ = true;
    }
    
    void setException(std::exception_ptr ex) {
        promise_.set_exception(ex);
        completed_ = true;
    }
    
    std::shared_future<int> getFuture() {
        return future_;
    }
    
    bool isCompleted() const {
        return completed_.load();
    }
};

TEST(PromiseBasicFunctionality) {
    SimplePromise promise;
    auto future = promise.getFuture();
    
    promise.setValue(123);
    
    EXPECT_EQ(future.get(), 123);
    EXPECT_TRUE(promise.isCompleted());
    
    std::cout << "✓ PromiseBasicFunctionality passed\n";
}

TEST(PromiseException) {
    SimplePromise promise;
    auto future = promise.getFuture();
    
    promise.setException(std::make_exception_ptr(std::runtime_error("promise error")));
    
    try {
        future.get();
        assert(false); // Should not reach here
    } catch (const std::runtime_error& e) {
        EXPECT_EQ(std::string(e.what()), "promise error");
    }
    
    std::cout << "✓ PromiseException passed\n";
}

// Main test runner
int main() {
    std::cout << "Running Minimal Async Tests...\n\n";
    
    try {
        test_BasicAsyncExecution();
        test_MultipleAsyncTasks();
        test_ExceptionHandling();
        test_ConcurrentAccess();
        test_TaskChaining();
        test_StressTest();
        test_PromiseBasicFunctionality();
        test_PromiseException();
        
        std::cout << "\n✅ All tests passed!\n";
        return 0;
    } catch (const std::exception& e) {
        std::cout << "\n❌ Test failed with exception: " << e.what() << "\n";
        return 1;
    } catch (...) {
        std::cout << "\n❌ Test failed with unknown exception\n";
        return 1;
    }
}
