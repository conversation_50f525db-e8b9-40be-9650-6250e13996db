#include <iostream>
#include <thread>
#include <chrono>
#include <atomic>
#include <vector>
#include <future>
#include <functional>
#include <cassert>
#include <queue>
#include <mutex>
#include <condition_variable>
#include <random>
#include <algorithm>
#include <numeric>

using namespace std::chrono_literals;
using namespace std::chrono;

// Performance measurement utilities
class PerformanceTimer {
private:
    high_resolution_clock::time_point start_time_;
    
public:
    PerformanceTimer() : start_time_(high_resolution_clock::now()) {}
    
    void reset() {
        start_time_ = high_resolution_clock::now();
    }
    
    double elapsedMs() const {
        auto end_time = high_resolution_clock::now();
        return duration_cast<microseconds>(end_time - start_time_).count() / 1000.0;
    }
    
    double elapsedUs() const {
        auto end_time = high_resolution_clock::now();
        return duration_cast<microseconds>(end_time - start_time_).count();
    }
};

// Test framework
#define PERF_TEST(name) void perf_test_##name()
#define EXPECT_LT(a, b) assert((a) < (b))
#define EXPECT_GT(a, b) assert((a) > (b))

// High-performance async executor for benchmarking
class HighPerformanceExecutor {
private:
    struct alignas(64) WorkerThread {
        std::queue<std::function<void()>> local_queue;
        std::mutex queue_mutex;
        std::condition_variable cv;
        std::thread thread;
        std::atomic<bool> running{true};
        std::atomic<size_t> tasks_processed{0};
        
        // Padding to avoid false sharing
        char padding[64 - sizeof(tasks_processed)];
    };
    
    std::vector<std::unique_ptr<WorkerThread>> workers_;
    std::atomic<size_t> next_worker_{0};
    std::atomic<size_t> total_tasks_submitted_{0};
    std::atomic<size_t> total_tasks_completed_{0};
    
public:
    HighPerformanceExecutor(size_t num_threads = std::thread::hardware_concurrency()) {
        workers_.reserve(num_threads);
        
        for (size_t i = 0; i < num_threads; ++i) {
            auto worker = std::make_unique<WorkerThread>();
            worker->thread = std::thread([&worker = *worker]() {
                while (worker.running.load(std::memory_order_relaxed)) {
                    std::unique_lock<std::mutex> lock(worker.queue_mutex);
                    worker.cv.wait(lock, [&worker]() { 
                        return !worker.local_queue.empty() || !worker.running.load(std::memory_order_relaxed); 
                    });
                    
                    if (!worker.running.load(std::memory_order_relaxed)) break;
                    
                    while (!worker.local_queue.empty()) {
                        auto task = std::move(worker.local_queue.front());
                        worker.local_queue.pop();
                        lock.unlock();
                        
                        try {
                            task();
                            worker.tasks_processed.fetch_add(1, std::memory_order_relaxed);
                        } catch (...) {
                            // Log error in real implementation
                        }
                        
                        lock.lock();
                    }
                }
            });
            workers_.push_back(std::move(worker));
        }
    }
    
    ~HighPerformanceExecutor() {
        for (auto& worker : workers_) {
            worker->running.store(false, std::memory_order_relaxed);
            worker->cv.notify_all();
            if (worker->thread.joinable()) {
                worker->thread.join();
            }
        }
    }
    
    template<typename F>
    void execute(F&& func) {
        size_t worker_idx = next_worker_.fetch_add(1, std::memory_order_relaxed) % workers_.size();
        auto& worker = *workers_[worker_idx];
        
        {
            std::lock_guard<std::mutex> lock(worker.queue_mutex);
            worker.local_queue.emplace(std::forward<F>(func));
        }
        worker.cv.notify_one();
        total_tasks_submitted_.fetch_add(1, std::memory_order_relaxed);
    }
    
    void waitForAll() {
        while (getTotalTasksCompleted() < total_tasks_submitted_.load(std::memory_order_relaxed)) {
            std::this_thread::sleep_for(100us);
        }
    }
    
    size_t getTotalTasksCompleted() const {
        size_t total = 0;
        for (const auto& worker : workers_) {
            total += worker->tasks_processed.load(std::memory_order_relaxed);
        }
        return total;
    }
    
    size_t getWorkerCount() const {
        return workers_.size();
    }
};

// Performance Tests
PERF_TEST(TaskSubmissionThroughput) {
    const size_t num_tasks = 100000;
    HighPerformanceExecutor executor(8);
    
    std::atomic<size_t> completed_tasks{0};
    
    PerformanceTimer timer;
    
    for (size_t i = 0; i < num_tasks; ++i) {
        executor.execute([&completed_tasks]() {
            completed_tasks.fetch_add(1, std::memory_order_relaxed);
        });
    }
    
    double submission_time = timer.elapsedMs();
    
    executor.waitForAll();
    double total_time = timer.elapsedMs();
    
    double submission_rate = num_tasks / (submission_time / 1000.0);
    double completion_rate = num_tasks / (total_time / 1000.0);
    
    assert(completed_tasks.load() == num_tasks);
    
    std::cout << "✓ TaskSubmissionThroughput: " << num_tasks << " tasks\n";
    std::cout << "  Submission rate: " << static_cast<int>(submission_rate) << " tasks/sec\n";
    std::cout << "  Completion rate: " << static_cast<int>(completion_rate) << " tasks/sec\n";
    std::cout << "  Total time: " << total_time << "ms\n";
}

PERF_TEST(TaskExecutionLatency) {
    const size_t num_samples = 10000;
    HighPerformanceExecutor executor(4);
    
    std::vector<double> latencies;
    latencies.reserve(num_samples);
    std::mutex latencies_mutex;
    
    for (size_t i = 0; i < num_samples; ++i) {
        auto start_time = high_resolution_clock::now();
        
        executor.execute([start_time, &latencies, &latencies_mutex]() {
            auto end_time = high_resolution_clock::now();
            double latency = duration_cast<microseconds>(end_time - start_time).count();
            
            std::lock_guard<std::mutex> lock(latencies_mutex);
            latencies.push_back(latency);
        });
        
        // Small delay to avoid overwhelming the system
        if (i % 1000 == 0) {
            std::this_thread::sleep_for(1ms);
        }
    }
    
    executor.waitForAll();
    
    std::sort(latencies.begin(), latencies.end());
    
    double avg_latency = std::accumulate(latencies.begin(), latencies.end(), 0.0) / latencies.size();
    double p50_latency = latencies[latencies.size() / 2];
    double p95_latency = latencies[static_cast<size_t>(latencies.size() * 0.95)];
    double p99_latency = latencies[static_cast<size_t>(latencies.size() * 0.99)];
    
    std::cout << "✓ TaskExecutionLatency: " << num_samples << " samples\n";
    std::cout << "  Average latency: " << avg_latency << "μs\n";
    std::cout << "  P50 latency: " << p50_latency << "μs\n";
    std::cout << "  P95 latency: " << p95_latency << "μs\n";
    std::cout << "  P99 latency: " << p99_latency << "μs\n";
}

PERF_TEST(ConcurrentWorkload) {
    const size_t num_producers = 8;
    const size_t tasks_per_producer = 10000;
    const size_t total_tasks = num_producers * tasks_per_producer;
    
    HighPerformanceExecutor executor(std::thread::hardware_concurrency());
    
    std::atomic<size_t> completed_tasks{0};
    std::vector<std::thread> producers;
    
    PerformanceTimer timer;
    
    // Start producer threads
    for (size_t p = 0; p < num_producers; ++p) {
        producers.emplace_back([&executor, &completed_tasks, tasks_per_producer]() {
            for (size_t i = 0; i < tasks_per_producer; ++i) {
                executor.execute([&completed_tasks]() {
                    // Simulate some work
                    volatile int result = 0;
                    for (int j = 0; j < 100; ++j) {
                        result += j;
                    }
                    completed_tasks.fetch_add(1, std::memory_order_relaxed);
                });
            }
        });
    }
    
    // Wait for all producers to finish submitting
    for (auto& producer : producers) {
        producer.join();
    }
    
    double submission_time = timer.elapsedMs();
    
    // Wait for all tasks to complete
    executor.waitForAll();
    double total_time = timer.elapsedMs();
    
    assert(completed_tasks.load() == total_tasks);
    
    double throughput = total_tasks / (total_time / 1000.0);
    
    std::cout << "✓ ConcurrentWorkload: " << total_tasks << " tasks from " << num_producers << " producers\n";
    std::cout << "  Submission time: " << submission_time << "ms\n";
    std::cout << "  Total time: " << total_time << "ms\n";
    std::cout << "  Throughput: " << static_cast<int>(throughput) << " tasks/sec\n";
}

PERF_TEST(MemoryUsageStability) {
    const size_t num_iterations = 1000;
    const size_t tasks_per_iteration = 1000;
    
    HighPerformanceExecutor executor(4);
    
    PerformanceTimer timer;
    
    for (size_t iter = 0; iter < num_iterations; ++iter) {
        std::atomic<size_t> completed{0};
        
        // Submit a batch of tasks
        for (size_t i = 0; i < tasks_per_iteration; ++i) {
            executor.execute([&completed]() {
                // Simulate work with some memory allocation
                std::vector<int> temp_data(100);
                std::iota(temp_data.begin(), temp_data.end(), 0);
                volatile int sum = std::accumulate(temp_data.begin(), temp_data.end(), 0);
                completed.fetch_add(1, std::memory_order_relaxed);
            });
        }
        
        // Wait for batch completion
        while (completed.load(std::memory_order_relaxed) < tasks_per_iteration) {
            std::this_thread::sleep_for(100us);
        }
        
        if (iter % 100 == 0) {
            std::cout << "  Completed iteration " << iter << "/" << num_iterations << "\n";
        }
    }
    
    double total_time = timer.elapsedMs();
    size_t total_tasks = num_iterations * tasks_per_iteration;
    
    std::cout << "✓ MemoryUsageStability: " << total_tasks << " tasks in " << num_iterations << " iterations\n";
    std::cout << "  Total time: " << total_time << "ms\n";
    std::cout << "  Average time per iteration: " << (total_time / num_iterations) << "ms\n";
}

PERF_TEST(ScalabilityTest) {
    const size_t num_tasks = 50000;
    std::vector<size_t> thread_counts = {1, 2, 4, 8, 16};
    
    std::cout << "✓ ScalabilityTest: Testing with different thread counts\n";
    
    for (size_t thread_count : thread_counts) {
        HighPerformanceExecutor executor(thread_count);
        std::atomic<size_t> completed_tasks{0};
        
        PerformanceTimer timer;
        
        for (size_t i = 0; i < num_tasks; ++i) {
            executor.execute([&completed_tasks]() {
                // Simulate CPU work
                volatile int result = 0;
                for (int j = 0; j < 1000; ++j) {
                    result += j * j;
                }
                completed_tasks.fetch_add(1, std::memory_order_relaxed);
            });
        }
        
        executor.waitForAll();
        double elapsed_time = timer.elapsedMs();
        
        assert(completed_tasks.load() == num_tasks);
        
        double throughput = num_tasks / (elapsed_time / 1000.0);
        
        std::cout << "  " << thread_count << " threads: " << elapsed_time << "ms, " 
                  << static_cast<int>(throughput) << " tasks/sec\n";
    }
}

// Main test runner
int main() {
    std::cout << "Running Performance and Stress Tests...\n";
    std::cout << "Hardware concurrency: " << std::thread::hardware_concurrency() << " threads\n\n";
    
    try {
        perf_test_TaskSubmissionThroughput();
        std::cout << "\n";
        
        perf_test_TaskExecutionLatency();
        std::cout << "\n";
        
        perf_test_ConcurrentWorkload();
        std::cout << "\n";
        
        perf_test_MemoryUsageStability();
        std::cout << "\n";
        
        perf_test_ScalabilityTest();
        
        std::cout << "\n✅ All performance tests completed!\n";
        return 0;
    } catch (const std::exception& e) {
        std::cout << "\n❌ Performance test failed with exception: " << e.what() << "\n";
        return 1;
    } catch (...) {
        std::cout << "\n❌ Performance test failed with unknown exception\n";
        return 1;
    }
}
